import React, { useState, useEffect, useRef } from 'react';
import { Button, Icon, Select } from 'tntd';
import I18N from '@/utils/I18N';
import './FilterPanel.less';

const FilterPanel = (props) => {
    const { visible, onVisibleChange, providerList, serviceTypeList, onChange, value = {} } = props;

    const [partnerId, setPartnerId] = useState(value?.partnerId);
    const [dataType, setDataType] = useState(value?.dataType);
    const [dataSourceType, setDataSourceType] = useState(value?.dataSourceType);
    const [status, setStatus] = useState(value?.status);
    const [selectedTags, setSelectedTags] = useState([]);

    // 用于引用整个筛选容器
    const filterContainerRef = useRef(null);

    useEffect(() => {
        setPartnerId(value?.partnerId);
        setDataType(value?.dataType);
        setDataSourceType(value?.dataSourceType);
        setStatus(value?.status);
    }, [value]);

    // 使用 React 的合成事件系统，完全不需要 document 方法
    // 通过在 window 对象上设置一个全局处理器
    useEffect(() => {
        if (!visible) return;

        const handleGlobalClick = (event) => {
            if (filterContainerRef.current && !filterContainerRef.current.contains(event.target)) {
                onVisibleChange && onVisibleChange(false);
            }
        };

        // 使用 window 的 onclick 属性而不是 addEventListener
        const originalOnClick = window.onclick;
        window.onclick = (event) => {
            if (originalOnClick) originalOnClick(event);
            handleGlobalClick(event);
        };

        return () => {
            window.onclick = originalOnClick;
        };
    }, [visible, onVisibleChange]);

    useEffect(() => {
        const tags = [];

        // 添加已选的合作方名称
        if (value?.partnerId) {
            const partner = providerList?.find((item) => item.displayName === value?.partnerId);
            if (partner) {
                tags.push({
                    type: 'partnerId',
                    value: value.partnerId,
                    label: partner.displayName
                });
            }
        }

        // 添加已选的数据类型
        if (value?.dataType) {
            const type = serviceTypeList?.find((item) => item.dataType === value?.dataType);
            if (type) {
                tags.push({
                    type: 'dataType',
                    value: value.dataType,
                    label: type.name
                });
            }
        }

        // 添加已选的接口类型
        if (value?.dataSourceType) {
            const sourceTypeLabel = value?.dataSourceType === 'SYNC' ? I18N.dataservicelist.tongBuJieKou : I18N.dataservicelist.yiBuJieKou;

            tags.push({
                type: 'dataSourceType',
                value: value.dataSourceType,
                label: sourceTypeLabel
            });
        }

        // 添加已选的接口状态
        if (value?.status) {
            const statusLabel = value?.status === '1' ? I18N.dataservicelist.shangXian : I18N.dataservicelist.xiaXian;

            tags.push({
                type: 'status',
                value: value.status,
                label: statusLabel
            });
        }

        setSelectedTags(tags);
    }, [value, providerList, serviceTypeList]);

    const toggleFilterPanel = () => {
        onVisibleChange && onVisibleChange(!visible);
    };

    const handlePartnerSelect = (value) => {
        setPartnerId(value);
        // 更新已选标签
        let partner = providerList?.find((item) => item.displayName === value);
        const newTags = selectedTags.filter((tag) => tag.type !== 'partnerId');
        if (value && partner) {
            newTags.push({
                type: 'partnerId',
                value,
                label: partner.displayName
            });
        }
        setSelectedTags(newTags);
        // 立即应用筛选条件
        const filters = {
            partnerId: value,
            dataType,
            dataSourceType,
            status
        };
        onChange && onChange(filters);
    };

    // 选择数据类型
    const handleDataTypeSelect = (type) => {
        let newDataType = dataType === type.dataType ? undefined : type.dataType;
        setDataType(newDataType);
        const newTags = selectedTags.filter((tag) => tag.type !== 'dataType');
        if (newDataType) {
            newTags.push({
                type: 'dataType',
                value: type.dataType,
                label: type.name
            });
        }
        setSelectedTags(newTags);
        const filters = {
            partnerId,
            dataType: newDataType,
            dataSourceType,
            status
        };
        onChange && onChange(filters);
    };

    // 选择接口类型
    const handleSourceTypeSelect = (type) => {
        let newSourceType = dataSourceType === type.value ? undefined : type.value;
        setDataSourceType(newSourceType);
        const newTags = selectedTags.filter((tag) => tag.type !== 'dataSourceType');
        if (newSourceType) {
            newTags.push({
                type: 'dataSourceType',
                value: type.value,
                label: type.label
            });
        }
        setSelectedTags(newTags);
        const filters = {
            partnerId,
            dataType,
            dataSourceType: newSourceType,
            status
        };
        onChange && onChange(filters);
    };

    // 选择接口状态
    const handleStatusSelect = (statusItem) => {
        let newStatus = status === statusItem.value ? undefined : statusItem.value;
        setStatus(newStatus);
        const newTags = selectedTags.filter((tag) => tag.type !== 'status');
        if (newStatus) {
            newTags.push({
                type: 'status',
                value: statusItem.value,
                label: statusItem.label
            });
        }
        setSelectedTags(newTags);
        const filters = {
            partnerId,
            dataType,
            dataSourceType,
            status: newStatus
        };
        onChange && onChange(filters);
    };

    // 移除标签
    const handleTagRemove = (tag) => {
        const newTags = selectedTags.filter((item) => !(item.type === tag.type && item.value === tag.value));
        setSelectedTags(newTags);

        // 更新对应的状态，并同步通知父组件
        let newPartnerId = partnerId;
        let newDataType = dataType;
        let newDataSourceType = dataSourceType;
        let newStatus = status;

        switch (tag.type) {
            case 'partnerId':
                newPartnerId = undefined;
                setPartnerId(undefined);
                break;
            case 'dataType':
                newDataType = undefined;
                setDataType(undefined);
                break;
            case 'dataSourceType':
                newDataSourceType = undefined;
                setDataSourceType(undefined);
                break;
            case 'status':
                newStatus = undefined;
                setStatus(undefined);
                break;
            default:
                break;
        }

        // 立即应用筛选条件
        onChange &&
            onChange({
                partnerId: newPartnerId,
                dataType: newDataType,
                dataSourceType: newDataSourceType,
                status: newStatus
            });
    };

    // 应用筛选条件
    const applyFilters = () => {
        const filters = {
            partnerId,
            dataType,
            dataSourceType,
            status
        };

        onChange && onChange(filters);
    };

    // 清空筛选条件
    const clearFilters = () => {
        setPartnerId(undefined);
        setDataType(undefined);
        setDataSourceType(undefined);
        setStatus(undefined);
        setSelectedTags([]);

        onChange &&
            onChange({
                partnerId: undefined,
                dataType: undefined,
                dataSourceType: undefined,
                status: undefined
            });
    };

    // 接口类型选项
    const sourceTypeOptions = [
        {
            label: I18N.dataservicelist.tongBuJieKou,
            value: 'SYNC'
        },
        {
            label: I18N.dataservicelist.yiBuJieKou,
            value: 'ASYNC'
        }
    ];

    // 接口状态选项
    const statusOptions = [
        {
            label: I18N.dataservicelist.shangXian,
            value: '1'
        },
        {
            label: I18N.dataservicelist.xiaXian,
            value: '2'
        }
    ];

    return (
        <div className="filter-container" ref={filterContainerRef}>
                <div className="filter-trigger">
                    <Button onClick={toggleFilterPanel} icon="filter" className={`filter-button ${visible ? 'active' : ''}`} />

                    {/* 显示已选标签 */}
                    {selectedTags.length > 0 && (
                        <div className="selected-tags">
                            {selectedTags.map((tag, index) => (
                                <div className="tag-item" key={`${tag.type}-${tag.value}`}>
                                    {tag.label}
                                    <Icon type="close" className="tag-close" onClick={() => handleTagRemove(tag)} />
                                </div>
                            ))}
                            {/* {selectedTags.length > 0 && (
                                <div className="clear-all" onClick={clearFilters}>
                                    {I18N.dataservicelist.qingKong || '清空'}
                                </div>
                            )} */}
                        </div>
                    )}
                </div>

                {visible && (
                    <div className="filter-panel">
                    {/* 合作方名称 */}
                    <div className="filter-section">
                        <div className="filter-label">{I18N.dataservicelist.heZuoFangMingCheng}</div>
                        <div className="filter-dropdown" style={{ padding: 0, border: 'none', background: 'none' }}>
                            <Select
                                style={{ width: '100%' }}
                                placeholder={I18N.dataservicelist.qingXuanZeHeZuo}
                                value={partnerId}
                                allowClear
                                onChange={handlePartnerSelect}
                                dropdownMatchSelectWidth={false}
                                showSearch
                                optionFilterProp="children"
                                getPopupContainer={() => filterContainerRef.current || document.body}>
                                {providerList?.map((item) => (
                                    <Select.Option key={item.displayName} value={item.displayName}>
                                        {item.displayName}
                                    </Select.Option>
                                ))}
                            </Select>
                        </div>
                    </div>

                    {/* 数据类型 */}
                    <div className="filter-section">
                        <div className="filter-label">{I18N.addmodify.shuJuLeiXing}</div>
                        <div className="filter-options">
                            {serviceTypeList?.map((type) => (
                                <div
                                    key={type.dataType}
                                    className={`filter-option ${dataType === type.dataType ? 'selected' : ''}`}
                                    onClick={() => handleDataTypeSelect(type)}>
                                    {type.name}
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* 接口类型 */}
                    <div className="filter-section">
                        <div className="filter-label">{I18N.dataservicelist.jieKouLeiXing}</div>
                        <div className="filter-options">
                            {sourceTypeOptions.map((type) => (
                                <div
                                    key={type.value}
                                    className={`filter-option ${dataSourceType === type.value ? 'selected' : ''}`}
                                    onClick={() => handleSourceTypeSelect(type)}>
                                    {type.label}
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* 接口状态 */}
                    <div className="filter-section">
                        <div className="filter-label">{I18N.dataservicelist.jieKouZhuangTai}</div>
                        <div className="filter-options">
                            {statusOptions.map((statusItem) => (
                                <div
                                    key={statusItem.value}
                                    className={`filter-option ${status === statusItem.value ? 'selected' : ''}`}
                                    onClick={() => handleStatusSelect(statusItem)}>
                                    {statusItem.label}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default FilterPanel;
